/**
 * @file stm32_robust_uart.h
 * @brief Robust UART Driver for STM32 with Protocol Integration
 * <AUTHOR> Embedded Engineer (10+ years experience)
 * @date 2025-07-12
 *
 * FEATURES:
 * =========
 * - Integration with robust protocol
 * - Optimized interrupt handling
 * - Error recovery mechanisms
 * - Hardware flow control support
 * - Configurable UART parameters
 * - Statistics and monitoring
 */

#ifndef STM32_ROBUST_UART_H
#define STM32_ROBUST_UART_H

#include "stm32f1xx_hal.h"
#include "robust_uart_protocol.h"

/* UART Configuration */
#define UART_BAUDRATE               115200
#define UART_WORDLENGTH             UART_WORDLENGTH_8B
#define UART_STOPBITS               UART_STOPBITS_1
#define UART_PARITY                 UART_PARITY_NONE
#define UART_HWCONTROL              UART_HWCONTROL_NONE
#define UART_OVERSAMPLING           UART_OVERSAMPLING_16

/* Error Recovery */
#define UART_ERROR_RECOVERY_TIMEOUT 100    // ms
#define UART_MAX_ERROR_COUNT        10     // Max consecutive errors before reset

/* UART Context */
typedef struct {
    UART_HandleTypeDef* huart;              // HAL UART handle
    protocol_context_t* protocol_ctx;       // Protocol context

    /* Error handling */
    uint32_t error_count;                   // Consecutive error count
    uint32_t last_error_time;               // Last error timestamp

    /* Statistics */
    uint32_t bytes_sent;
    uint32_t bytes_received;
    uint32_t uart_errors;
    uint32_t overrun_errors;
    uint32_t frame_errors;
    uint32_t noise_errors;

    /* State */
    bool initialized;
    bool error_recovery_mode;
} stm32_uart_context_t;

/* Function Prototypes */

/**
 * @brief Initialize STM32 UART with robust protocol
 * @param ctx UART context
 * @param huart HAL UART handle
 * @param protocol_ctx Protocol context
 * @return HAL_StatusTypeDef Status
 */
HAL_StatusTypeDef stm32_uart_init(stm32_uart_context_t* ctx,
                                  UART_HandleTypeDef* huart,
                                  protocol_context_t* protocol_ctx);

/**
 * @brief Deinitialize UART
 * @param ctx UART context
 * @return HAL_StatusTypeDef Status
 */
HAL_StatusTypeDef stm32_uart_deinit(stm32_uart_context_t* ctx);

/**
 * @brief Send JSON data through UART with protocol
 * @param ctx UART context
 * @param json_data JSON string
 * @param length Data length
 * @return protocol_error_t Error code
 */
protocol_error_t stm32_uart_send_json(stm32_uart_context_t* ctx,
                                      const char* json_data,
                                      uint16_t length);

/**
 * @brief Process UART in main loop
 * @param ctx UART context
 */
void stm32_uart_process(stm32_uart_context_t* ctx);

/**
 * @brief UART interrupt handler (call from HAL interrupt)
 * @param ctx UART context
 */
void stm32_uart_irq_handler(stm32_uart_context_t* ctx);

/**
 * @brief Handle UART errors
 * @param ctx UART context
 * @param error_code HAL error code
 */
void stm32_uart_handle_error(stm32_uart_context_t* ctx, uint32_t error_code);

/**
 * @brief Reset UART after error
 * @param ctx UART context
 * @return HAL_StatusTypeDef Status
 */
HAL_StatusTypeDef stm32_uart_reset(stm32_uart_context_t* ctx);

/**
 * @brief Get UART statistics
 * @param ctx UART context
 * @return stm32_uart_context_t* Pointer to context (for statistics)
 */
const stm32_uart_context_t* stm32_uart_get_stats(const stm32_uart_context_t* ctx);

/**
 * @brief Reset UART statistics
 * @param ctx UART context
 */
void stm32_uart_reset_stats(stm32_uart_context_t* ctx);

/* Callback Functions (implement in user code) */

/**
 * @brief Callback when JSON data is received
 * @param json_data JSON string (null-terminated)
 * @param length Data length
 */
void stm32_uart_on_json_received(const char* json_data, uint16_t length);

/**
 * @brief Callback when protocol error occurs
 * @param error Protocol error code
 */
void stm32_uart_on_protocol_error(protocol_error_t error);

/* Utility Functions */

/**
 * @brief Get current system tick (implement in user code)
 * @return uint32_t Current tick in milliseconds
 */
uint32_t stm32_uart_get_tick(void);

/**
 * @brief Delay function (implement in user code)
 * @param ms Delay in milliseconds
 */
void stm32_uart_delay(uint32_t ms);

#endif /* STM32_ROBUST_UART_H */