---
name: 'Bug report'
about: 'Create a report to help us improve the quality of our software'
title: ''
labels: ''
assignees: ''
---

**Caution**

The Issues are strictly limited for the reporting of problem encountered with the software provided in this project.
For any other problem related to the STM32 product, the performance, the hardware characteristics and boards, the tools the environment in general, please post your report to the **ST Community** in the STM32 MCUs dedicated [page](https://community.st.com/s/group/0F90X000000AXsASAW/stm32-mcus).

**Describe the set-up**

 * The board (either ST RPN reference or your custom board)
 * IDE or at least the compiler and its version

**Describe the bug (skip if none)**

A clear and concise description of what the bug is.

**How to reproduce the bug (skip if none)**

1. Indicate the global behavior of your application project
2. List the modules that you suspect to be the cause of the problem (Drivers, BSP, MW...)
3. Describe the use case that generates the problem
4. How we can reproduce the problem


**Additional context**

If you have a first analysis, an enhancement, a fix or a patch, thank you to share your proposal.

**Screenshots**

If applicable, add screenshots to help explain your problem.
