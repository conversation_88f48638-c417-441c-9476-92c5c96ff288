/**
 * @file rak3172_example_usage.ino
 * @brief Example usage of robust UART communication for RAK3172
 * <AUTHOR> Embedded Engineer (10+ years experience)
 * @date 2025-07-12
 *
 * USAGE INSTRUCTIONS:
 * ==================
 * 1. Include rak3172_robust_uart.h and rak3172_robust_uart.cpp in your project
 * 2. Replace the existing UART handling code with this example
 * 3. Configure Serial1 for communication with STM32
 * 4. Implement the callback functions as needed
 * 5. Upload to RAK3172 module
 */

#include "rak3172_robust_uart.h"
#include "RAKLorawan.h"

/* Global variables */
rak_uart_context_t uart_ctx;

/* LoRaWAN configuration (from your existing code) */
#define ABP_PERIOD   (300000)
#define ABP_BAND     (RAK_REGION_AS923_2)

/* Example data structures */
typedef struct {
    uint8_t header_device;
    uint16_t status_code;
    float sensor_value;
    uint32_t timestamp;
} lorawan_data_t;

void setup() {
    // Initialize Serial for debugging
    Serial.begin(115200, RAK_AT_MODE);
    delay(1000);

    // Initialize robust UART communication with STM32
    rak_error_t result = rak_uart_init(&uart_ctx, &Serial1);
    if (result != RAK_OK) {
        Serial.println("Failed to initialize robust UART");
        return;
    }

    // Initialize LoRaWAN (from your existing code)
    setup_lorawan();

    // Send initial handshake to STM32
    StaticJsonDocument<200> handshake;
    handshake["command"] = "handshake";
    handshake["device"] = "RAK3172";
    handshake["version"] = "1.0";
    handshake["timestamp"] = millis();

    rak_uart_send_json_object(&uart_ctx, handshake);

    Serial.println("RAK3172 robust UART initialized");
}

void loop() {
    // Process UART communication (MUST be called frequently)
    rak_uart_process(&uart_ctx);

    // Your existing LoRaWAN logic here
    handle_lorawan_communication();

    // Example: Send periodic status to STM32
    static unsigned long last_status_time = 0;
    if ((millis() - last_status_time) > 30000) {  // Every 30 seconds
        send_status_to_stm32();
        last_status_time = millis();
    }

    // Example: Check for downlink data
    handle_downlink_data();

    delay(10);  // Small delay to prevent watchdog issues
}

/**
 * @brief Setup LoRaWAN connection
 */
void setup_lorawan() {
    // Your existing LoRaWAN setup code
    // Example implementation:
    /*
    if (!api.lorawan.band.set(ABP_BAND)) {
        Serial.println("LoRaWan ABP - set band is incorrect!");
        return;
    }

    // Set ABP keys (replace with your actual keys)
    uint8_t node_device_addr[4] = {0x01, 0x02, 0x03, 0x04};
    uint8_t node_app_skey[16] = {0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08,
                                 0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F, 0x10};
    uint8_t node_nwk_skey[16] = {0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08,
                                 0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F, 0x10};

    if (!api.lorawan.daddr.set(node_device_addr, 4)) {
        Serial.println("LoRaWan ABP - set device addr is incorrect!");
        return;
    }

    if (!api.lorawan.appskey.set(node_app_skey, 16)) {
        Serial.println("LoRaWan ABP - set application session key is incorrect!");
        return;
    }

    if (!api.lorawan.nwkskey.set(node_nwk_skey, 16)) {
        Serial.println("LoRaWan ABP - set network session key is incorrect!");
        return;
    }

    if (!api.lorawan.njm.set(RAK_LORA_ABP)) {
        Serial.println("LoRaWan ABP - set network join mode is incorrect!");
        return;
    }

    if (!api.lorawan.join()) {
        Serial.println("LoRaWan ABP - join fail!");
        return;
    }
    */

    Serial.println("LoRaWAN setup completed");
}

/**
 * @brief Handle LoRaWAN communication
 */
void handle_lorawan_communication() {
    // Your existing LoRaWAN communication logic
    // Example: Check for confirmed frames, handle retries, etc.
}

/**
 * @brief Send status to STM32
 */
void send_status_to_stm32() {
    StaticJsonDocument<200> status;
    status["command"] = "status_report";
    status["lorawan_connected"] = api.lorawan.njs.get();
    status["signal_strength"] = api.lorawan.rssi.get();
    status["battery_level"] = 85;  // Example value
    status["uptime"] = millis();
    status["timestamp"] = millis();

    rak_error_t result = rak_uart_send_json_object(&uart_ctx, status);
    if (result != RAK_OK) {
        Serial.print("Failed to send status: ");
        Serial.println(result);
    }
}

/**
 * @brief Handle downlink data from LoRaWAN
 */
void handle_downlink_data() {
    // Check for downlink data
    if (api.lorawan.recv.get() > 0) {
        uint8_t recv_data[64];
        int recv_len = api.lorawan.recv.get(recv_data, sizeof(recv_data));

        if (recv_len > 0) {
            // Forward downlink data to STM32
            StaticJsonDocument<300> downlink;
            downlink["command"] = "downlink_data";
            downlink["length"] = recv_len;

            // Convert binary data to hex string
            String hex_data = "";
            for (int i = 0; i < recv_len; i++) {
                if (recv_data[i] < 16) hex_data += "0";
                hex_data += String(recv_data[i], HEX);
            }
            downlink["data"] = hex_data;
            downlink["timestamp"] = millis();

            rak_uart_send_json_object(&uart_ctx, downlink);
        }
    }
}

/**
 * @brief Send data to LoRaWAN
 * @param data Data structure to send
 * @return bool Success status
 */
bool send_to_lorawan(const lorawan_data_t* data) {
    if (!data) {
        return false;
    }

    uint8_t payload[16];
    int payload_len = 0;

    // Pack data into payload
    payload[payload_len++] = data->header_device;
    payload[payload_len++] = (uint8_t)(data->status_code & 0xFF);
    payload[payload_len++] = (uint8_t)(data->status_code >> 8);

    // Pack float as 4 bytes
    memcpy(&payload[payload_len], &data->sensor_value, sizeof(float));
    payload_len += sizeof(float);

    // Pack timestamp as 4 bytes
    memcpy(&payload[payload_len], &data->timestamp, sizeof(uint32_t));
    payload_len += sizeof(uint32_t);

    // Send via LoRaWAN
    bool result = api.lorawan.send(payload_len, payload, 2, true, 0);

    if (result) {
        Serial.println("Data sent to LoRaWAN successfully");
    } else {
        Serial.println("Failed to send data to LoRaWAN");
    }

    return result;
}

/**
 * @brief Callback when JSON data is received from STM32
 * @param json_data JSON string (null-terminated)
 * @param length Data length
 * @param json_doc Parsed JSON document
 */
void rak_uart_on_json_received(const char* json_data,
                               uint16_t length,
                               const JsonDocument& json_doc) {
    Serial.print("Received from STM32: ");
    Serial.println(json_data);

    // Process different command types
    const char* command = json_doc["command"];
    if (command) {
        if (strcmp(command, "send_lorawan") == 0) {
            // Send data to LoRaWAN
            lorawan_data_t data;
            data.header_device = json_doc["header"] | 4;
            data.status_code = json_doc["status"] | 200;
            data.sensor_value = json_doc["sensor_value"] | 0.0f;
            data.timestamp = json_doc["timestamp"] | millis();

            send_to_lorawan(&data);

            // Send acknowledgment back to STM32
            StaticJsonDocument<100> ack;
            ack["ack"] = "send_lorawan";
            ack["success"] = true;
            ack["timestamp"] = millis();
            rak_uart_send_json_object(&uart_ctx, ack);
        }
        else if (strcmp(command, "get_lorawan_status") == 0) {
            // Send LoRaWAN status
            StaticJsonDocument<200> status;
            status["response"] = "lorawan_status";
            status["connected"] = api.lorawan.njs.get();
            status["rssi"] = api.lorawan.rssi.get();
            status["snr"] = api.lorawan.snr.get();
            status["timestamp"] = millis();
            rak_uart_send_json_object(&uart_ctx, status);
        }
        else if (strcmp(command, "reset") == 0) {
            // Perform system reset
            api.system.reboot();
        }
    }
}

/**
 * @brief Callback when protocol error occurs
 * @param error Error code
 */
void rak_uart_on_error(rak_error_t error) {
    Serial.print("UART Protocol Error: ");
    Serial.println(error);

    // Handle different error types
    switch (error) {
        case RAK_ERROR_CRC_MISMATCH:
            Serial.println("CRC mismatch - data corruption detected");
            break;

        case RAK_ERROR_TIMEOUT:
            Serial.println("Communication timeout");
            break;

        case RAK_ERROR_BUFFER_FULL:
            Serial.println("Buffer overflow");
            break;

        case RAK_ERROR_JSON_PARSE:
            Serial.println("JSON parsing error");
            break;

        default:
            Serial.println("Unknown error");
            break;
    }

    // Optionally send error notification to STM32
    StaticJsonDocument<100> error_msg;
    error_msg["error"] = "rak_protocol";
    error_msg["code"] = error;
    error_msg["timestamp"] = millis();
    rak_uart_send_json_object(&uart_ctx, error_msg);
}