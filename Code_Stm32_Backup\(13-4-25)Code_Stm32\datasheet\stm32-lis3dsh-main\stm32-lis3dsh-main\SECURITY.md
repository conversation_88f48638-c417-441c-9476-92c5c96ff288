# Report potential product security vulnerabilities

ST places a high priority on security, and our Product Security Incident
Response Team (PSIRT) is committed to rapidly addressing potential security
vulnerabilities affecting our products. PSIRT's long history and vast experience
in security allows ST to perform clear analyses and provide appropriate guidance
on mitigations and solutions when applicable.

If you wish to report potential security vulnerabilities regarding our products,
**please do not report them through public GitHub issues.** Instead, we
encourage you to report them to our ST PSIRT following the process described at:
**https://www.st.com/content/st_com/en/security/report-vulnerabilities.html**

### IMPORTANT - READ CAREFULLY:

STMicroelectronics International N.V., on behalf of itself, its affiliates and
subsidiaries, (collectively “ST”) takes all potential security vulnerability
reports or other related communications (“Report(s)”) seriously. In order to
review Your Report (the terms “You” and “Yours” include your employer, and all
affiliates, subsidiaries and related persons or entities) and take actions as
deemed appropriate, ST requires that we have the rights and Your permission to
do so.

As such, by submitting Your Report to ST, You agree that You have the right to
do so, and You grant to ST the rights to use the Report for purposes related to
security vulnerability analysis, testing, correction, patching, reporting and
any other related purpose or function.

By submitting Your Report, You agree that ST’s
[Privacy Policy](https://www.st.com/content/st_com/en/common/privacy-portal.html)
applies to all related communications.
