/**
 * @file stm32_example_usage.c
 * @brief Example usage of robust UART communication for STM32
 * <AUTHOR> Embedded Engineer (10+ years experience)
 * @date 2025-07-12
 *
 * USAGE INSTRUCTIONS:
 * ==================
 * 1. Include this file in your STM32 project
 * 2. Replace the existing UART handling code
 * 3. Configure UART4 in STM32CubeMX (115200 baud, 8N1)
 * 4. Enable UART4 global interrupt
 * 5. Call robust_uart_example_init() in main()
 * 6. Call robust_uart_example_process() in main loop
 * 7. Implement the callback functions as needed
 */

#include "stm32_robust_uart.h"
#include "cJSON.h"

/* Global variables */
static stm32_uart_context_t uart_ctx;
static protocol_context_t protocol_ctx;
extern UART_HandleTypeDef huart4;  // Assuming UART4 is used

/* Example JSON data structures */
typedef struct {
    int dimming;
    int lamp_status;
    float temperature;
    uint32_t timestamp;
} device_data_t;

/**
 * @brief Initialize robust UART communication
 * @return HAL_StatusTypeDef Status
 */
HAL_StatusTypeDef robust_uart_example_init(void) {
    // Initialize the robust UART system
    HAL_StatusTypeDef status = stm32_uart_init(&uart_ctx, &huart4, &protocol_ctx);

    if (status == HAL_OK) {
        // Send initial status message
        const char* init_msg = "{\"status\":\"initialized\",\"version\":\"1.0\"}";
        stm32_uart_send_json(&uart_ctx, init_msg, strlen(init_msg));
    }

    return status;
}

/**
 * @brief Process UART communication (call in main loop)
 */
void robust_uart_example_process(void) {
    stm32_uart_process(&uart_ctx);

    // Example: Send periodic status every 10 seconds
    static uint32_t last_status_time = 0;
    uint32_t current_time = HAL_GetTick();

    if ((current_time - last_status_time) > 10000) {  // 10 seconds
        device_data_t data = {
            .dimming = 75,
            .lamp_status = 1,
            .temperature = 25.5f,
            .timestamp = current_time
        };

        send_device_status(&data);
        last_status_time = current_time;
    }
}

/**
 * @brief Send device status as JSON
 * @param data Device data structure
 * @return protocol_error_t Error code
 */
protocol_error_t send_device_status(const device_data_t* data) {
    if (!data) {
        return PROTOCOL_ERROR_INVALID_FRAME;
    }

    // Create JSON object
    cJSON* json = cJSON_CreateObject();
    if (!json) {
        return PROTOCOL_ERROR_BUFFER_FULL;
    }

    // Add data fields
    cJSON_AddNumberToObject(json, "dimming", data->dimming);
    cJSON_AddNumberToObject(json, "lamp_status", data->lamp_status);
    cJSON_AddNumberToObject(json, "temperature", data->temperature);
    cJSON_AddNumberToObject(json, "timestamp", data->timestamp);

    // Convert to string
    char* json_string = cJSON_Print(json);
    protocol_error_t result = PROTOCOL_ERROR_BUFFER_FULL;

    if (json_string) {
        result = stm32_uart_send_json(&uart_ctx, json_string, strlen(json_string));
        free(json_string);
    }

    cJSON_Delete(json);
    return result;
}

/**
 * @brief UART interrupt handler (call from stm32f1xx_it.c)
 */
void UART4_IRQHandler(void) {
    stm32_uart_irq_handler(&uart_ctx);
}

/**
 * @brief Callback when JSON data is received
 * @param json_data JSON string (null-terminated)
 * @param length Data length
 */
void stm32_uart_on_json_received(const char* json_data, uint16_t length) {
    // Parse received JSON
    cJSON* json = cJSON_Parse(json_data);
    if (!json) {
        return;  // Invalid JSON
    }

    // Process different command types
    cJSON* command = cJSON_GetObjectItem(json, "command");
    if (cJSON_IsString(command)) {
        const char* cmd = command->valuestring;

        if (strcmp(cmd, "set_dimming") == 0) {
            cJSON* value = cJSON_GetObjectItem(json, "value");
            if (cJSON_IsNumber(value)) {
                int dimming = value->valueint;
                set_dimming_level(dimming);

                // Send acknowledgment
                char ack_msg[100];
                snprintf(ack_msg, sizeof(ack_msg),
                        "{\"ack\":\"set_dimming\",\"value\":%d}", dimming);
                stm32_uart_send_json(&uart_ctx, ack_msg, strlen(ack_msg));
            }
        }
        else if (strcmp(cmd, "get_status") == 0) {
            // Send current status
            device_data_t current_data = {
                .dimming = get_current_dimming(),
                .lamp_status = get_lamp_status(),
                .temperature = get_temperature(),
                .timestamp = HAL_GetTick()
            };
            send_device_status(&current_data);
        }
        else if (strcmp(cmd, "reset") == 0) {
            // Perform system reset
            NVIC_SystemReset();
        }
    }

    cJSON_Delete(json);
}

/**
 * @brief Callback when protocol error occurs
 * @param error Protocol error code
 */
void stm32_uart_on_protocol_error(protocol_error_t error) {
    // Log error or take corrective action
    switch (error) {
        case PROTOCOL_ERROR_CRC_MISMATCH:
            // CRC error - data corruption
            break;

        case PROTOCOL_ERROR_TIMEOUT:
            // Communication timeout
            break;

        case PROTOCOL_ERROR_BUFFER_FULL:
            // Buffer overflow
            break;

        default:
            break;
    }

    // Optionally send error notification
    char error_msg[100];
    snprintf(error_msg, sizeof(error_msg),
            "{\"error\":\"protocol\",\"code\":%d}", error);
    stm32_uart_send_json(&uart_ctx, error_msg, strlen(error_msg));
}

/* Example hardware control functions (implement according to your hardware) */

void set_dimming_level(int dimming) {
    // Implement PWM control for dimming
    if (dimming >= 0 && dimming <= 100) {
        // Example: Set PWM duty cycle
        // __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_1, (dimming * 1000) / 100);
    }
}

int get_current_dimming(void) {
    // Return current dimming level
    // Example: return (__HAL_TIM_GET_COMPARE(&htim1, TIM_CHANNEL_1) * 100) / 1000;
    return 75;  // Example value
}

int get_lamp_status(void) {
    // Return lamp on/off status
    // Example: return HAL_GPIO_ReadPin(LAMP_GPIO_Port, LAMP_Pin);
    return 1;  // Example: lamp is on
}

float get_temperature(void) {
    // Read temperature sensor
    // Example: ADC reading and conversion
    return 25.5f;  // Example temperature
}