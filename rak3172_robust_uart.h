/**
 * @file rak3172_robust_uart.h
 * @brief Robust UART Handler for RAK3172 with Protocol Integration
 * <AUTHOR> Embedded Engineer (10+ years experience)
 * @date 2025-07-12
 *
 * FEATURES:
 * =========
 * - Arduino-compatible implementation
 * - Integration with robust protocol
 * - Buffer management and overflow protection
 * - Error detection and recovery
 * - Statistics and monitoring
 * - Non-blocking operation
 */

#ifndef RAK3172_ROBUST_UART_H
#define RAK3172_ROBUST_UART_H

#include <Arduino.h>
#include <ArduinoJson.h>

/* Configuration */
#define RAK_UART_BAUDRATE           115200
#define RAK_RX_BUFFER_SIZE          512     // Must be power of 2
#define RAK_TX_BUFFER_SIZE          512     // Must be power of 2
#define RAK_JSON_BUFFER_SIZE        256
#define RAK_MAX_FRAME_SIZE          256

/* Protocol Constants (matching STM32 side) */
#define RAK_PROTOCOL_STX            0x02    // Start of Text
#define RAK_PROTOCOL_ETX            0x03    // End of Text
#define RAK_PROTOCOL_ACK            0x06    // Acknowledgment
#define RAK_PROTOCOL_NAK            0x15    // Negative Acknowledgment

/* Timing Constants */
#define RAK_ACK_TIMEOUT_MS          1000    // Wait for ACK timeout
#define RAK_RETRY_MAX_COUNT         3       // Maximum retry attempts
#define RAK_RETRY_BASE_DELAY_MS     100     // Base delay for exponential backoff
#define RAK_FRAME_TIMEOUT_MS        500     // Frame reception timeout

/* Error Codes */
typedef enum {
    RAK_OK = 0,
    RAK_ERROR_INVALID_FRAME,
    RAK_ERROR_CRC_MISMATCH,
    RAK_ERROR_TIMEOUT,
    RAK_ERROR_BUFFER_FULL,
    RAK_ERROR_INVALID_LENGTH,
    RAK_ERROR_SEQUENCE_ERROR,
    RAK_ERROR_JSON_PARSE
} rak_error_t;

/* Protocol States */
typedef enum {
    RAK_STATE_IDLE,
    RAK_STATE_RECEIVING,
    RAK_STATE_WAIT_ACK,
    RAK_STATE_ERROR
} rak_state_t;

/* Ring Buffer Structure */
typedef struct {
    uint8_t buffer[RAK_RX_BUFFER_SIZE];
    volatile uint16_t head;
    volatile uint16_t tail;
    volatile uint16_t count;
} rak_ring_buffer_t;

/* Statistics */
typedef struct {
    uint32_t frames_sent;
    uint32_t frames_received;
    uint32_t frames_retried;
    uint32_t crc_errors;
    uint32_t timeout_errors;
    uint32_t buffer_overflows;
    uint32_t sequence_errors;
    uint32_t json_errors;
} rak_stats_t;

/* RAK UART Context */
typedef struct {
    HardwareSerial* serial;             // Serial port (Serial1)
    rak_state_t state;
    uint8_t tx_sequence;                // Next sequence number to send
    uint8_t rx_sequence;                // Expected sequence number
    uint32_t last_tx_time;              // Last transmission time
    uint32_t frame_start_time;          // Frame reception start time
    uint8_t retry_count;                // Current retry count
    rak_ring_buffer_t rx_buffer;        // Reception ring buffer
    rak_stats_t stats;                  // Protocol statistics

    /* Frame parsing state */
    uint8_t rx_frame_buffer[RAK_MAX_FRAME_SIZE];
    uint16_t rx_frame_index;
    bool frame_in_progress;

    /* JSON handling */
    StaticJsonDocument<RAK_JSON_BUFFER_SIZE> json_doc;

    /* State */
    bool initialized;
} rak_uart_context_t;

/* Function Prototypes */

/**
 * @brief Initialize RAK3172 UART with robust protocol
 * @param ctx RAK UART context
 * @param serial Serial port (usually Serial1)
 * @return rak_error_t Error code
 */
rak_error_t rak_uart_init(rak_uart_context_t* ctx, HardwareSerial* serial);

/**
 * @brief Deinitialize RAK UART
 * @param ctx RAK UART context
 */
void rak_uart_deinit(rak_uart_context_t* ctx);

/**
 * @brief Send JSON data through UART with protocol
 * @param ctx RAK UART context
 * @param json_data JSON string
 * @param length Data length
 * @return rak_error_t Error code
 */
rak_error_t rak_uart_send_json(rak_uart_context_t* ctx,
                               const char* json_data,
                               uint16_t length);

/**
 * @brief Send JSON object through UART with protocol
 * @param ctx RAK UART context
 * @param json_obj JSON object
 * @return rak_error_t Error code
 */
rak_error_t rak_uart_send_json_object(rak_uart_context_t* ctx,
                                      const JsonDocument& json_obj);

/**
 * @brief Process UART in main loop (call frequently)
 * @param ctx RAK UART context
 */
void rak_uart_process(rak_uart_context_t* ctx);

/**
 * @brief Get RAK UART statistics
 * @param ctx RAK UART context
 * @return rak_stats_t* Pointer to statistics
 */
const rak_stats_t* rak_uart_get_stats(const rak_uart_context_t* ctx);

/**
 * @brief Reset RAK UART statistics
 * @param ctx RAK UART context
 */
void rak_uart_reset_stats(rak_uart_context_t* ctx);

/* Callback Functions (implement in user code) */

/**
 * @brief Callback when JSON data is received
 * @param json_data JSON string (null-terminated)
 * @param length Data length
 * @param json_doc Parsed JSON document
 */
void rak_uart_on_json_received(const char* json_data,
                               uint16_t length,
                               const JsonDocument& json_doc);

/**
 * @brief Callback when protocol error occurs
 * @param error Error code
 */
void rak_uart_on_error(rak_error_t error);

/* Utility Functions */
uint16_t rak_calculate_crc16(const uint8_t* data, uint16_t length);
bool rak_ring_buffer_put(rak_ring_buffer_t* rb, uint8_t data);
bool rak_ring_buffer_get(rak_ring_buffer_t* rb, uint8_t* data);
uint16_t rak_ring_buffer_available(const rak_ring_buffer_t* rb);
void rak_ring_buffer_clear(rak_ring_buffer_t* rb);

#endif /* RAK3172_ROBUST_UART_H */