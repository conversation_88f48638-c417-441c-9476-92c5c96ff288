/**
 * @file stm32_robust_uart.c
 * @brief Robust UART Driver Implementation for STM32
 * <AUTHOR> Embedded Engineer (10+ years experience)
 * @date 2025-07-12
 *
 * IMPLEMENTATION NOTES:
 * ====================
 * - Optimized for STM32F1xx series
 * - Interrupt-driven with error recovery
 * - Integration with robust protocol layer
 * - Comprehensive error handling
 * - Performance monitoring
 */

#include "stm32_robust_uart.h"

/* Static variables for interrupt context */
static stm32_uart_context_t* g_uart_ctx = NULL;

/* Internal function prototypes */
static void uart_send_byte_callback(uint8_t byte);
static HAL_StatusTypeDef uart_configure_hardware(UART_HandleTypeDef* huart);

/**
 * @brief Send byte callback for protocol layer
 * @param byte Byte to send
 */
static void uart_send_byte_callback(uint8_t byte) {
    if (g_uart_ctx && g_uart_ctx->huart) {
        // Use HAL blocking transmit for single byte (fast enough for protocol)
        HAL_UART_Transmit(g_uart_ctx->huart, &byte, 1, 10);
        g_uart_ctx->bytes_sent++;
    }
}

/**
 * @brief Configure UART hardware parameters
 * @param huart HAL UART handle
 * @return HAL_StatusTypeDef Status
 */
static HAL_StatusTypeDef uart_configure_hardware(UART_HandleTypeDef* huart) {
    huart->Init.BaudRate = UART_BAUDRATE;
    huart->Init.WordLength = UART_WORDLENGTH;
    huart->Init.StopBits = UART_STOPBITS;
    huart->Init.Parity = UART_PARITY;
    huart->Init.Mode = UART_MODE_TX_RX;
    huart->Init.HwFlowCtl = UART_HWCONTROL;
    huart->Init.OverSampling = UART_OVERSAMPLING;

    return HAL_UART_Init(huart);
}

/**
 * @brief Initialize STM32 UART with robust protocol
 * @param ctx UART context
 * @param huart HAL UART handle
 * @param protocol_ctx Protocol context
 * @return HAL_StatusTypeDef Status
 */
HAL_StatusTypeDef stm32_uart_init(stm32_uart_context_t* ctx,
                                  UART_HandleTypeDef* huart,
                                  protocol_context_t* protocol_ctx) {
    if (!ctx || !huart || !protocol_ctx) {
        return HAL_ERROR;
    }

    // Initialize context
    memset(ctx, 0, sizeof(stm32_uart_context_t));
    ctx->huart = huart;
    ctx->protocol_ctx = protocol_ctx;

    // Configure hardware
    HAL_StatusTypeDef status = uart_configure_hardware(huart);
    if (status != HAL_OK) {
        return status;
    }

    // Initialize protocol with callbacks
    protocol_init(protocol_ctx,
                  uart_send_byte_callback,
                  stm32_uart_get_tick,
                  stm32_uart_delay,
                  stm32_uart_on_json_received,
                  stm32_uart_on_protocol_error);

    // Set global context for interrupt handling
    g_uart_ctx = ctx;

    // Enable UART interrupts
    __HAL_UART_ENABLE_IT(huart, UART_IT_RXNE);  // RX interrupt
    __HAL_UART_ENABLE_IT(huart, UART_IT_ERR);   // Error interrupt

    ctx->initialized = true;

    return HAL_OK;
}

/**
 * @brief Deinitialize UART
 * @param ctx UART context
 * @return HAL_StatusTypeDef Status
 */
HAL_StatusTypeDef stm32_uart_deinit(stm32_uart_context_t* ctx) {
    if (!ctx || !ctx->huart) {
        return HAL_ERROR;
    }

    // Disable interrupts
    __HAL_UART_DISABLE_IT(ctx->huart, UART_IT_RXNE);
    __HAL_UART_DISABLE_IT(ctx->huart, UART_IT_ERR);

    // Deinitialize hardware
    HAL_StatusTypeDef status = HAL_UART_DeInit(ctx->huart);

    // Clear global context
    if (g_uart_ctx == ctx) {
        g_uart_ctx = NULL;
    }

    ctx->initialized = false;

    return status;
}

/**
 * @brief Send JSON data through UART with protocol
 * @param ctx UART context
 * @param json_data JSON string
 * @param length Data length
 * @return protocol_error_t Error code
 */
protocol_error_t stm32_uart_send_json(stm32_uart_context_t* ctx,
                                      const char* json_data,
                                      uint16_t length) {
    if (!ctx || !ctx->initialized || !json_data) {
        return PROTOCOL_ERROR_INVALID_FRAME;
    }

    return protocol_send_data(ctx->protocol_ctx, json_data, length);
}

/**
 * @brief Process UART in main loop
 * @param ctx UART context
 */
void stm32_uart_process(stm32_uart_context_t* ctx) {
    if (!ctx || !ctx->initialized) {
        return;
    }

    // Process protocol state machine
    protocol_process(ctx->protocol_ctx);

    // Handle error recovery
    if (ctx->error_recovery_mode) {
        uint32_t current_time = stm32_uart_get_tick();
        if ((current_time - ctx->last_error_time) > UART_ERROR_RECOVERY_TIMEOUT) {
            // Try to recover from error
            if (stm32_uart_reset(ctx) == HAL_OK) {
                ctx->error_recovery_mode = false;
                ctx->error_count = 0;
            }
        }
    }
}

/**
 * @brief UART interrupt handler (call from HAL interrupt)
 * @param ctx UART context
 */
void stm32_uart_irq_handler(stm32_uart_context_t* ctx) {
    if (!ctx || !ctx->huart) {
        return;
    }

    UART_HandleTypeDef* huart = ctx->huart;
    uint32_t isrflags = READ_REG(huart->Instance->SR);
    uint32_t cr1its = READ_REG(huart->Instance->CR1);
    uint32_t errorflags = (isrflags & (uint32_t)(USART_SR_PE | USART_SR_FE | USART_SR_ORE | USART_SR_NE));

    /* Handle errors first */
    if (errorflags != RESET) {
        stm32_uart_handle_error(ctx, errorflags);
        return;
    }

    /* Handle RX interrupt */
    if (((isrflags & USART_SR_RXNE) != RESET) && ((cr1its & USART_CR1_RXNEIE) != RESET)) {
        // Read data register to clear RXNE flag
        uint8_t received_byte = (uint8_t)(huart->Instance->DR & 0xFF);

        // Process byte through protocol layer
        protocol_process_rx_byte(ctx->protocol_ctx, received_byte);

        ctx->bytes_received++;
    }
}

/**
 * @brief Handle UART errors
 * @param ctx UART context
 * @param error_code HAL error code
 */
void stm32_uart_handle_error(stm32_uart_context_t* ctx, uint32_t error_code) {
    if (!ctx) {
        return;
    }

    ctx->uart_errors++;
    ctx->error_count++;
    ctx->last_error_time = stm32_uart_get_tick();

    // Clear error flags
    __HAL_UART_CLEAR_FLAG(ctx->huart, UART_CLEAR_PEF);
    __HAL_UART_CLEAR_FLAG(ctx->huart, UART_CLEAR_FEF);
    __HAL_UART_CLEAR_FLAG(ctx->huart, UART_CLEAR_NEF);
    __HAL_UART_CLEAR_FLAG(ctx->huart, UART_CLEAR_OREF);

    // Count specific error types
    if (error_code & USART_SR_ORE) {
        ctx->overrun_errors++;
    }
    if (error_code & USART_SR_FE) {
        ctx->frame_errors++;
    }
    if (error_code & USART_SR_NE) {
        ctx->noise_errors++;
    }

    // Enter error recovery mode if too many consecutive errors
    if (ctx->error_count >= UART_MAX_ERROR_COUNT) {
        ctx->error_recovery_mode = true;
    }
}

/**
 * @brief Reset UART after error
 * @param ctx UART context
 * @return HAL_StatusTypeDef Status
 */
HAL_StatusTypeDef stm32_uart_reset(stm32_uart_context_t* ctx) {
    if (!ctx || !ctx->huart) {
        return HAL_ERROR;
    }

    // Disable interrupts
    __HAL_UART_DISABLE_IT(ctx->huart, UART_IT_RXNE);
    __HAL_UART_DISABLE_IT(ctx->huart, UART_IT_ERR);

    // Reset UART peripheral
    HAL_StatusTypeDef status = HAL_UART_DeInit(ctx->huart);
    if (status != HAL_OK) {
        return status;
    }

    // Reconfigure hardware
    status = uart_configure_hardware(ctx->huart);
    if (status != HAL_OK) {
        return status;
    }

    // Re-enable interrupts
    __HAL_UART_ENABLE_IT(ctx->huart, UART_IT_RXNE);
    __HAL_UART_ENABLE_IT(ctx->huart, UART_IT_ERR);

    return HAL_OK;
}

/**
 * @brief Get UART statistics
 * @param ctx UART context
 * @return stm32_uart_context_t* Pointer to context (for statistics)
 */
const stm32_uart_context_t* stm32_uart_get_stats(const stm32_uart_context_t* ctx) {
    return ctx;
}

/**
 * @brief Reset UART statistics
 * @param ctx UART context
 */
void stm32_uart_reset_stats(stm32_uart_context_t* ctx) {
    if (!ctx) {
        return;
    }

    ctx->bytes_sent = 0;
    ctx->bytes_received = 0;
    ctx->uart_errors = 0;
    ctx->overrun_errors = 0;
    ctx->frame_errors = 0;
    ctx->noise_errors = 0;
    ctx->error_count = 0;

    // Reset protocol statistics too
    protocol_reset_stats(ctx->protocol_ctx);
}

/* Weak implementations of callback functions (can be overridden by user) */

/**
 * @brief Weak implementation of JSON received callback
 * @param json_data JSON string (null-terminated)
 * @param length Data length
 */
__weak void stm32_uart_on_json_received(const char* json_data, uint16_t length) {
    // User should implement this function
    // Example: Parse JSON and handle received data
    UNUSED(json_data);
    UNUSED(length);
}

/**
 * @brief Weak implementation of protocol error callback
 * @param error Protocol error code
 */
__weak void stm32_uart_on_protocol_error(protocol_error_t error) {
    // User should implement this function
    // Example: Log error, trigger recovery, etc.
    UNUSED(error);
}

/**
 * @brief Weak implementation of get tick function
 * @return uint32_t Current tick in milliseconds
 */
__weak uint32_t stm32_uart_get_tick(void) {
    return HAL_GetTick();
}

/**
 * @brief Weak implementation of delay function
 * @param ms Delay in milliseconds
 */
__weak void stm32_uart_delay(uint32_t ms) {
    HAL_Delay(ms);
}