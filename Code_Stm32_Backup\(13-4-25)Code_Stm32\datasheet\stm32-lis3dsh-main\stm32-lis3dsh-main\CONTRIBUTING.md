## Contributing guide

This document serves as a checklist before contributing to this repository.
It includes links to read up on if topics are unclear to you.

This guide mainly focuses on steps to be followed to submit an issue or a pull-request.

### 1. Before opening an issue

To report a bug or a request please file an issue in the right repository
(example for [lis3dsh](https://github.com/STMicroelectronics/lis3dsh/issues/new/choose)).

Please check the following boxes before posting an issue:
- [ ] `Make sure you are using the latest commit (major releases are tagged, but corrections are available as new commits).`
- [ ] `Make sure your issue is a question/feedback/suggestions RELATED TO the software provided in this repository.` Otherwise, it should be submitted to the ST Community under the MCU topic [page](https://community.st.com/s/group/0F90X000000AXsASAW/stm32-mcus).
- [ ] `Make sure your issue is not already reported/fixed on GitHub or discussed on a previous issue.` Please refer to this [dashboard](https://github.com/orgs/STMicroelectronics/projects/2) for the list of issues and pull-requests. Do not forget to browse into the **closed** issues.

### 2. Posting the issue
When you have checked the previous boxes. You will find two templates (Bug Report or Other Issue) available in the **Issues** tab of the repository.

### 3. Pull Requests
Pull-requests are **not** accepted on this repository. Please use **issues** to report any bug or request.

### 4. How to proceed

* We recommend to engage first a communication thru an issue, in order to present your proposal. Just to confirm that it corresponds to STMicroelectronics domain or scope.
* Then fork the project to your GitHub account to further develop your contribution. Please use the latest commit version.
* Please, submit one Pull Request for one new feature or proposal. This will ease the analysis and the final merge if accepted.
