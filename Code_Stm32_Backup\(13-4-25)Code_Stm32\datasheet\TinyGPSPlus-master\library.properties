name=TinyGPSPlus
version=1.1.0
author=<PERSON><PERSON>
maintainer=<PERSON><PERSON><<EMAIL>>
sentence=TinyGPSPlus provides object-oriented parsing of GPS (NMEA) sentences
paragraph=NMEA is the standard format GPS devices use to report location, time, altitude, etc. TinyGPSPlus is a compact, resilient library that parses the most common NMEA 'sentences' used: GGA and RMC. It can also be customized to extract data from *any* compliant sentence.
category=Communication
url=https://github.com/mikalhart/TinyGPSPlus
architectures=*
